# Buildtools for GitLab CI/CD

stages:
  - build

variables:
  IMAGE_NAME: buildtools
  IMAGE_TAG: backend
  GCP_PROJECT_ID: prod-95octane-app
  IMAGE_PREFIX: asia-southeast1-docker.pkg.dev/prod-95octane-app/public

build-backend:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - apk update && apk add curl bash python3 py3-pip
    - curl -sSL https://sdk.cloud.google.com | bash -s -- --disable-prompts --install-dir=/usr/local
    - export PATH=$PATH:/usr/local/google-cloud-sdk/bin
    - gcloud auth activate-service-account --key-file=${GOOGLE_APPLICATION_CREDENTIALS}
    - gcloud config set project ${GCP_PROJECT_ID}
    - gcloud auth configure-docker asia-southeast1-docker.pkg.dev
  script:
    - docker build -t ${IMAGE_PREFIX}/${IMAGE_NAME}:${IMAGE_TAG} -f backend/dockerfile .
    - docker push ${IMAGE_PREFIX}/${IMAGE_NAME}:${IMAGE_TAG}
