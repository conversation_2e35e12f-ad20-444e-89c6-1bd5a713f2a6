FROM chainguard/wolfi-base:latest

# Install dependencies, docker-cli-tools, nodejs & npm
RUN apk update \ 
  && apk upgrade --latest \
  && apk add --no-cache \
  iptables \
  nodejs \
  npm \
  bash \
  git \
  curl \
  python3 py3-pip \
  docker-cli docker-cli-buildx docker-compose docker-rootless \
  kmod \
  tini \
  && rm -rf /var/cache/apk/*

# Create necessary directories
RUN mkdir -p /var/run

# Install gcloud-cli
RUN curl -sSL https://sdk.cloud.google.com | bash -s -- --disable-prompts --install-dir=/usr/local

# Install firebase-cli
RUN npm cache clean --force && npm install -g firebase-tools

# Copy docker-entrypoint.sh
COPY ./backend/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Create a symlink for docker-init pointing to tini
RUN ln -s /usr/bin/tini /usr/bin/docker-init

ENTRYPOINT ["/usr/bin/bash", "/usr/local/bin/docker-entrypoint.sh"]
CMD ["bash"]
